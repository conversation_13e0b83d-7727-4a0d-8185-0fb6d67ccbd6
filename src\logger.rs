use std::sync::Mutex;
use windows::core::PCWSTR;

#[repr(C)]
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum VelociTunLogLevel {
    Info = 0,
    Warn = 1,
    Err = 2,
}

pub type VelociTunLoggerCallback =
    Option<extern "system" fn(level: VelociTunLogLevel, timestamp: u64, message: PCWSTR)>;

static LOGGER: Mutex<VelociTunLoggerCallback> = Mutex::new(None);

pub fn init_logger() {
    // Initialize default logger if needed
}

#[no_mangle]
pub extern "system" fn VelociTunSetLogger(new_logger: VelociTunLoggerCallback) {
    let mut logger = LOGGER.lock().unwrap();
    *logger = new_logger;
}

pub fn log_message(level: VelociTunLogLevel, message: &str) {
    // First, use the standard log crate so it shows up in env_logger
    match level {
        VelociTunLogLevel::Info => log::info!("{}", message),
        VelociTunLogLevel::Warn => log::warn!("{}", message),
        VelociTunLogLevel::Err => log::error!("{}", message),
    }

    // Also call the custom callback if set
    let logger = LOGGER.lock().unwrap();
    if let Some(callback) = *logger {
        let timestamp = get_timestamp();
        let wide_message = to_wide_string(message);
        let pcwstr = PCWSTR(wide_message.as_ptr());
        callback(level, timestamp, pcwstr);
    }
}

fn get_timestamp() -> u64 {
    use std::time::{SystemTime, UNIX_EPOCH};

    let duration = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default();

    // Convert to Windows FILETIME (100ns intervals since 1601-01-01)
    (duration.as_nanos() / 100) as u64 + 116444736000000000
}

fn to_wide_string(s: &str) -> Vec<u16> {
    s.encode_utf16().chain(std::iter::once(0)).collect()
}

macro_rules! log_info {
    ($($arg:tt)*) => {
        crate::logger::log_message(crate::logger::VelociTunLogLevel::Info, &format!($($arg)*))
    };
}

macro_rules! log_warn {
    ($($arg:tt)*) => {
        crate::logger::log_message(crate::logger::VelociTunLogLevel::Warn, &format!($($arg)*))
    };
}

macro_rules! log_error {
    ($($arg:tt)*) => {
        crate::logger::log_message(crate::logger::VelociTunLogLevel::Err, &format!($($arg)*))
    };
}

pub(crate) use log_error;
pub(crate) use log_info;
pub(crate) use log_warn;
