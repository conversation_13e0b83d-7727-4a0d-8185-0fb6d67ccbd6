use crate::{
    adapter::Adapter,
    error::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>eloci<PERSON>unR<PERSON>ult},
    logger::*,
    packet::Packet,
    ring::Ring,
};
use std::{
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Condvar, Mutex,
    },
    time::{Duration, Instant},
};

/// High-performance session for concurrent packet I/O
pub struct Session {
    adapter: Arc<Adapter>,
    send_ring: Arc<Ring>,
    receive_ring: Arc<Ring>,
    read_notify: Arc<(Mutex<bool>, Condvar)>,
    capacity: u32,
    active: AtomicBool,
}

impl Session {
    /// Create a new high-performance session
    pub fn new(adapter: Arc<Adapter>, capacity: u32) -> VelociTunResult<Self> {
        Self::validate_capacity(capacity)?;

        log_info!(
            "Creating session for adapter '{}' with capacity {}",
            adapter.name(),
            capacity
        );

        let send_ring = Ring::new(capacity)?;
        let receive_ring = Ring::new(capacity)?;

        Ok(Session {
            adapter,
            send_ring: Arc::new(send_ring),
            receive_ring: Arc::new(receive_ring),
            read_notify: Arc::new((Mutex::new(false), Condvar::new())),
            capacity,
            active: AtomicBool::new(true),
        })
    }

    /// Get the adapter associated with this session
    pub fn adapter(&self) -> &Adapter {
        &self.adapter
    }

    /// Get the ring buffer capacity
    pub fn capacity(&self) -> u32 {
        self.capacity
    }

    /// Check if the session is active
    pub fn is_active(&self) -> bool {
        self.active.load(Ordering::Acquire)
    }

    /// Send a packet (thread-safe)
    pub fn send_packet(&self, data: &[u8]) -> VelociTunResult<()> {
        if !self.is_active() {
            return Err(VelociTunError::HandleEof);
        }

        if data.len() > crate::packet::MAX_IP_PACKET_SIZE {
            return Err(VelociTunError::InvalidParameter(
                "Packet too large".to_string(),
            ));
        }

        // Create packet
        let packet = Packet::new(data)?;

        // Try to allocate space in send ring
        if self.send_ring.available_space() < packet.total_size() as u32 {
            return Err(VelociTunError::BufferOverflow);
        }

        // Write packet to ring
        self.send_ring.write_packet(&packet)?;

        log_info!("Sent packet of {} bytes", data.len());
        Ok(())
    }

    /// Receive a packet (thread-safe, blocking)
    pub fn receive_packet(&self) -> VelociTunResult<Vec<u8>> {
        self.receive_packet_timeout(Duration::from_secs(u64::MAX))
    }

    /// Receive a packet with timeout (thread-safe)
    pub fn receive_packet_timeout(&self, timeout: Duration) -> VelociTunResult<Vec<u8>> {
        if !self.is_active() {
            return Err(VelociTunError::HandleEof);
        }

        let start_time = Instant::now();
        let (lock, cvar) = &*self.read_notify;

        loop {
            // Try to read from ring
            if let Ok(packet) = self.receive_ring.read_packet() {
                log_info!("Received packet of {} bytes", packet.data().len());
                return Ok(packet.data().to_vec());
            }

            // Check timeout
            if start_time.elapsed() >= timeout {
                return Err(VelociTunError::WindowsApi(windows::core::Error::from(
                    windows::Win32::Foundation::ERROR_TIMEOUT,
                )));
            }

            // Wait for notification with timeout
            let remaining_timeout = timeout.saturating_sub(start_time.elapsed());
            if remaining_timeout.is_zero() {
                return Err(VelociTunError::WindowsApi(windows::core::Error::from(
                    windows::Win32::Foundation::ERROR_TIMEOUT,
                )));
            }

            let guard = lock.lock().unwrap();
            let wait_result = cvar.wait_timeout(guard, remaining_timeout).unwrap();

            if wait_result.1.timed_out() {
                return Err(VelociTunError::WindowsApi(windows::core::Error::from(
                    windows::Win32::Foundation::ERROR_TIMEOUT,
                )));
            }
        }
    }

    /// Try to receive a packet without blocking (thread-safe)
    pub fn try_receive_packet(&self) -> VelociTunResult<Option<Vec<u8>>> {
        if !self.is_active() {
            return Err(VelociTunError::HandleEof);
        }

        match self.receive_ring.read_packet() {
            Ok(packet) => {
                log_info!("Received packet of {} bytes", packet.data().len());
                Ok(Some(packet.data().to_vec()))
            }
            Err(VelociTunError::NoMoreItems) => Ok(None),
            Err(e) => Err(e),
        }
    }

    /// Notify waiting receivers that data is available
    pub fn notify_receivers(&self) {
        let (lock, cvar) = &*self.read_notify;
        let _guard = lock.lock().unwrap();
        cvar.notify_all();
    }

    /// Get statistics about the session
    pub fn stats(&self) -> SessionStats {
        SessionStats {
            send_ring_used: self.send_ring.used_space(),
            send_ring_available: self.send_ring.available_space(),
            receive_ring_used: self.receive_ring.used_space(),
            receive_ring_available: self.receive_ring.available_space(),
            capacity: self.capacity,
            is_active: self.is_active(),
        }
    }

    /// Shutdown the session
    pub fn shutdown(&self) {
        log_info!(
            "Shutting down sync session for adapter '{}'",
            self.adapter.name()
        );

        self.active.store(false, Ordering::Release);

        // Notify all waiting threads
        self.notify_receivers();
    }

    /// Create a thread-safe packet sender
    pub fn create_sender(&self) -> PacketSender {
        PacketSender {
            session: Arc::new(self.clone()),
        }
    }

    /// Create a thread-safe packet receiver
    pub fn create_receiver(&self) -> PacketReceiver {
        PacketReceiver {
            session: Arc::new(self.clone()),
        }
    }

    fn validate_capacity(capacity: u32) -> VelociTunResult<()> {
        if !capacity.is_power_of_two() {
            return Err(VelociTunError::InvalidRingCapacity(capacity));
        }

        if !(crate::ring::MIN_RING_CAPACITY..=crate::ring::MAX_RING_CAPACITY).contains(&capacity) {
            return Err(VelociTunError::InvalidRingCapacity(capacity));
        }

        Ok(())
    }
}

impl Clone for Session {
    fn clone(&self) -> Self {
        Self {
            adapter: self.adapter.clone(),
            send_ring: self.send_ring.clone(),
            receive_ring: self.receive_ring.clone(),
            read_notify: self.read_notify.clone(),
            capacity: self.capacity,
            active: AtomicBool::new(self.active.load(Ordering::Acquire)),
        }
    }
}

impl Drop for Session {
    fn drop(&mut self) {
        self.shutdown();
        log_info!("Session dropped for adapter '{}'", self.adapter.name());
    }
}

/// Session statistics
#[derive(Debug, Clone)]
pub struct SessionStats {
    pub send_ring_used: u32,
    pub send_ring_available: u32,
    pub receive_ring_used: u32,
    pub receive_ring_available: u32,
    pub capacity: u32,
    pub is_active: bool,
}

/// Thread-safe packet sender
#[derive(Clone)]
pub struct PacketSender {
    session: Arc<Session>,
}

impl PacketSender {
    /// Send a packet
    pub fn send(&self, data: &[u8]) -> VelociTunResult<()> {
        self.session.send_packet(data)
    }

    /// Send multiple packets in batch
    pub fn send_batch(&self, packets: &[&[u8]]) -> VelociTunResult<()> {
        for packet in packets {
            self.send(packet)?;
        }
        Ok(())
    }

    /// Check if sender is active
    pub fn is_active(&self) -> bool {
        self.session.is_active()
    }

    /// Get session statistics
    pub fn stats(&self) -> SessionStats {
        self.session.stats()
    }
}

/// Thread-safe packet receiver
#[derive(Clone)]
pub struct PacketReceiver {
    session: Arc<Session>,
}

impl PacketReceiver {
    /// Receive a packet (blocking)
    pub fn receive(&self) -> VelociTunResult<Vec<u8>> {
        self.session.receive_packet()
    }

    /// Receive a packet with timeout
    pub fn receive_timeout(&self, timeout: Duration) -> VelociTunResult<Vec<u8>> {
        self.session.receive_packet_timeout(timeout)
    }

    /// Try to receive a packet without blocking
    pub fn try_receive(&self) -> VelociTunResult<Option<Vec<u8>>> {
        self.session.try_receive_packet()
    }

    /// Receive multiple packets in batch
    pub fn receive_batch(&self, max_packets: usize) -> VelociTunResult<Vec<Vec<u8>>> {
        let mut packets = Vec::new();

        for _ in 0..max_packets {
            match self.try_receive()? {
                Some(packet) => packets.push(packet),
                None => break,
            }
        }

        Ok(packets)
    }

    /// Check if receiver is active
    pub fn is_active(&self) -> bool {
        self.session.is_active()
    }

    /// Get session statistics
    pub fn stats(&self) -> SessionStats {
        self.session.stats()
    }
}

// Safety: Session is designed to be thread-safe
unsafe impl Send for Session {}
unsafe impl Sync for Session {}

unsafe impl Send for PacketSender {}
unsafe impl Sync for PacketSender {}

unsafe impl Send for PacketReceiver {}
unsafe impl Sync for PacketReceiver {}
