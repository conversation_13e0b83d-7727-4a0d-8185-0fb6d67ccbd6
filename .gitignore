# Visual Studio Local Files
.vs/
*.user

# Build Output
/dist
/Debug
/Release

# Driver Build Output (all platforms and configurations)
/driver/x64/
/driver/Win32/
/driver/ARM64/
/driver/Debug/
/driver/Release/

# Driver Build Intermediate Files
/driver/*-intermediate/
/driver/*.log
/driver/*.wrn
/driver/*.err
/driver/*.pdb
/driver/*.idb
/driver/*.ilk
/driver/*.exp
/driver/*.lib
/driver/*.obj
/driver/*.res
/driver/*.tlog

# Static Driver Verifier Output
/driver/sdv/
/driver/smvbuild.log
/driver/smvstats.txt

# CodeQL Output
/driver/driver.sarif
/driver/vc140.pdb

# Driver Verification Log
/driver/velocitun.DVL.XML

# Driver Package Files (generated)
/driver/*.cat
# /driver/*.inf
/driver/*.sys

# MSBuild Files
/driver/*.vcxproj.user
/driver/*.vcxproj.filters
/driver/.vs/

# Temporary files
*~

# Rust Build Output
target/

# Generated Resources
resources/velocitun-amd64.sys
resources/velocitun-amd64.inf
resources/velocitun-amd64.cat
